package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.ctrip.dcs.dsp.delay.infrastructure.dto.GaoDeFutureMonitorDTO;
import com.ctrip.dcs.dsp.delay.infrastructure.util.DoubleUtils;
import com.ctrip.dcs.dsp.delay.infrastructure.util.MonitorUtil;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.basic.map.application.service.interfaces.BaseGpsDTO;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.dto.GaoDeFutureDTO;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.DcsMapDomainServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.OchGeoServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy;
import com.ctrip.dcs.dsp.delay.limit.LbsRateLimiter;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.util.GeoHashUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.location.application.service.interfaces.dto.ExtendInfoDTO;
import com.ctrip.dcs.location.application.service.message.QueryPredictRouteRequestType;
import com.ctrip.dcs.location.application.service.message.QueryPredictRouteResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.igt.geo.interfaces.dto.BaseLatLngPairDTO;
import com.ctrip.igt.geo.interfaces.dto.GaodeDistanceInfoDTO;
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchRequestType;
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchResponseType;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 */
@Component
public class GeoGatewayImpl implements GeoGateway {

    private static final Logger logger = LoggerFactory.getLogger(GeoGatewayImpl.class);

    @Autowired
    private OchGeoServiceProxy ochGeoServiceProxy;

    @Autowired
    private TRocksProviderProxy trocksProviderProxy;

    @Autowired
    private Cache<String, String> caffeineCache;

    @Autowired
    private LbsRateLimiter rateLimiter;

    @Autowired
    private DcsMapDomainServiceProxy dcsMapDomainServiceProxy;

    @Resource(name = "gaoDeFutureThreadPool")
    private ExecutorService gaoDeFutureThreadPool;

    @Resource(name = "delayGaoDeFutureThreadPool")
    private ExecutorService delayGaoDeFutureThreadPool;

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Override
    public List<Route> queryRoutes(Integer cityId, String orderId, List<Position> positions) {
        List<Route> routes = queryRoutes(cityId, positions);
        CompletableFuture.runAsync(() -> {
            queryRoutesNew(cityId, orderId, positions, routes);
        }, gaoDeFutureThreadPool);
        return routes;
    }

    @Override
    public List<Route> queryRoutes(Integer cityId, List<Position> positions) {
        List<Route> routes = Lists.newArrayList();
        List<List<Position>> partition = Lists.partition(positions, 99);
        for (List<Position> list : partition) {
            try {
                double acquire = rateLimiter.acquire();
                logger.info("GeoGatewayImpl.queryRoutes", "get token.time:{}", acquire);
                QueryDistanceBatchRequestType request = buildQueryDistanceBatchRequestType(cityId, list);
                QueryDistanceBatchResponseType response = ochGeoServiceProxy.queryDistanceBatch(request);
                routes.addAll(buildRoute(response));
            } catch (Exception e) {
                logger.error("GeoGatewayImpl.queryRoutes", e);
            }
        }
        return routes;
    }

    public void queryRoutesNew(Integer cityId, String userOrderId, List<Position> positions, List<Route> routes) {

        try {

            if (BooleanUtils.isNotTrue(delayDspCommonQConfig.isGaoDeFutureSwitch())) {
                return;
            }

            if (CollectionUtils.isEmpty(positions)) {
                return;
            }

            List<Route> result = new ArrayList<>();
            List<CompletableFuture<Route>> futures = new ArrayList<>();

            for (Position position : positions) {
                CompletableFuture<Route> future = CompletableFuture.supplyAsync(() -> {
                    Transaction transaction = Cat.newTransaction("queryRoutesNew", cityId.toString());
                    try {
                        return queryEstimateRoute(cityId, userOrderId, position);
                    } catch (Exception e) {
                        logger.error("GeoGatewayImpl.queryRoutesNew", e);
                        transaction.setStatus(e);
                        return null;
                    } finally {
                        transaction.complete();
                    }
                }, delayGaoDeFutureThreadPool);
                futures.add(future);
            }

            // 等待所有异步任务完成并收集结果
            for (CompletableFuture<Route> future : futures) {
                try {
                    Route route = future.get();
                    if (route != null) {
                        result.add(route);
                    }
                } catch (Exception e) {
                    logger.error("GeoGatewayImpl.queryRoutesNew - future.get() error", e);
                }
            }

            if (CollectionUtils.isEmpty(result) || CollectionUtils.isEmpty(routes)) {
                logger.warn("GeoGatewayImpl.queryRoutesNew", "result or routes is empty");
                return;
            }

            Map<String, Route> collect1 = result.stream().collect(Collectors.toMap(Route::getTimePeriodHash, r -> r, (r1, r2) -> r1));
            Map<String, Route> collect = routes.stream().collect(Collectors.toMap(Route::getHash, r -> r, (r1, r2) -> r1));
            for (Position position : positions) {
                if (Objects.isNull(position.getDepartureTime())) {
                    continue;
                }
                String fromHash = GeoHashUtil.buildGeoHash(position.getFromLongitude(), position.getFromLatitude());
                String toHash = GeoHashUtil.buildGeoHash(position.getToLongitude(), position.getToLatitude());
                String hash = Position.hash(fromHash, toHash);
                String timeHash = Position.timeHash(fromHash, toHash, position.getDepartureTime());
                if (collect.containsKey(hash) && collect1.containsKey(timeHash)) {
                    Route realTimeRoute = collect.get(hash);
                    Route futureRoute = collect1.get(timeHash);
                    logger.info("GeoGatewayImpl.queryRoutesNew", "realTimeRoute:{}, futureRoute:{}", JsonUtil.toJson(realTimeRoute), JsonUtil.toJson(futureRoute));
                    MetricsUtil.recordValue("query.route.future.success", 1);
                    // 发送延迟消息，延迟时间为出发时间与现在时间的时间差
                    GaoDeFutureDTO gaoDeFutureDTO = buildGaoDeInfo(cityId, userOrderId, position, realTimeRoute, futureRoute);
                    sendDelayedMonitorMessage(gaoDeFutureDTO);
                    monitor(gaoDeFutureDTO);
                }
            }
        } catch (Exception e) {
            logger.warn("GeoGatewayImpl.queryRoutesNew", e);
        }
    }

    private static void monitor( GaoDeFutureDTO gaoDeFutureDTO) {
        GaoDeFutureMonitorDTO gaoDeFutureMonitorDTO = new GaoDeFutureMonitorDTO();
        try {
            gaoDeFutureMonitorDTO.setCityId(gaoDeFutureDTO.getCityId().toString());
            gaoDeFutureMonitorDTO.setOrderId(gaoDeFutureDTO.getUserOrderId());
            gaoDeFutureMonitorDTO.setDepartureTime(DateUtil.formatDate(gaoDeFutureDTO.getDepartureTime(), DateUtil.DATE_FMT));
            gaoDeFutureMonitorDTO.setOrigin(GeoHashUtil.buildGeoHash(gaoDeFutureDTO.getFromLongitude(), gaoDeFutureDTO.getFromLatitude()));
            gaoDeFutureMonitorDTO.setDestination(GeoHashUtil.buildGeoHash(gaoDeFutureDTO.getToLongitude(), gaoDeFutureDTO.getToLatitude()));
            gaoDeFutureMonitorDTO.setRealTimeDistance(DoubleUtils.toString(gaoDeFutureDTO.getRealTimeDistance()));
            gaoDeFutureMonitorDTO.setRealTimeDuration(DoubleUtils.toString(gaoDeFutureDTO.getRealTimeDistance()));
            gaoDeFutureMonitorDTO.setFutureDistance(DoubleUtils.toString(gaoDeFutureDTO.getFutureDistance()));
            gaoDeFutureMonitorDTO.setFutureDuration(DoubleUtils.toString(gaoDeFutureDTO.getFutureDuration()));
            gaoDeFutureMonitorDTO.setExecuteTime(DateUtil.formatDate(gaoDeFutureDTO.getExecuteTime(), DateUtil.DATE_FMT));
            gaoDeFutureMonitorDTO.setRealTimeDiffDuration(DoubleUtils.toString(gaoDeFutureDTO.getRealTimeDuration() - gaoDeFutureDTO.getFutureDuration()));
            gaoDeFutureMonitorDTO.setScene("delay-gaode-realtime-monitor");
            MonitorUtil.monitor(gaoDeFutureMonitorDTO);
        } catch (NumberFormatException e) {
            logger.warn("monitor error",  e);
        }
    }

    @NotNull
    private static GaoDeFutureDTO buildGaoDeInfo(Integer cityId, String userOrderId, Position position, Route realTimeRoute, Route futureRoute) {
        GaoDeFutureDTO gaoDeFutureDTO = new GaoDeFutureDTO();
        gaoDeFutureDTO.setCityId(cityId);
        gaoDeFutureDTO.setUserOrderId(userOrderId);
        gaoDeFutureDTO.setFromLongitude(position.getFromLongitude());
        gaoDeFutureDTO.setFromLatitude(position.getFromLatitude());
        gaoDeFutureDTO.setFromCoordsys(position.getFromCoordsys());
        gaoDeFutureDTO.setToLongitude(position.getToLongitude());
        gaoDeFutureDTO.setToLatitude(position.getToLatitude());
        gaoDeFutureDTO.setToCoordsys(position.getToCoordsys());
        gaoDeFutureDTO.setDepartureTime(position.getDepartureTime());
        gaoDeFutureDTO.setRealTimeDistance(realTimeRoute.getDistance());
        gaoDeFutureDTO.setRealTimeDuration(realTimeRoute.getDistance());
        gaoDeFutureDTO.setFutureDistance(futureRoute.getDistance());
        gaoDeFutureDTO.setFutureDuration(futureRoute.getDuration());
        gaoDeFutureDTO.setExecuteTime(new Date());
        return gaoDeFutureDTO;
    }

    private Route queryEstimateRoute(Integer cityId, String userOrderId, Position position) {
        metricLog(cityId, position);

        if (BooleanUtils.isNotTrue(delayDspCommonQConfig.isUseGaodeFuture())) {
            logger.info("GeoGatewayImpl.queryEstimateRoute", "isUseGaodeFuture is false");
            return null;
        }

        if (Objects.isNull(position.getDepartureTime())) {
            logger.warn("GeoGatewayImpl.queryEstimateRoute", "position.getDepartureTime() is null");
            return null;
        }

        if (BooleanUtils.isTrue(delayDspCommonQConfig.isTop20CitySwitch()) && !delayDspCommonQConfig.top20CityIds().contains(cityId)) {
            logger.info("GeoGatewayImpl.queryEstimateRoute", "cityId is not in top20CityIds");
            return null;
        }

        // 判断当前时间是否在配置的可调用时段内
        if (BooleanUtils.isNotTrue(delayDspCommonQConfig.isWithinCallableTimePeriod())) {
            logger.info("GeoGatewayImpl.queryEstimateRoute", "current time is not within callable time period");
            return null;
        }


        boolean b = rateLimiter.futureAcquire();
        if (BooleanUtils.isNotTrue(b)) {
            logger.warn("GeoGatewayImpl.queryEstimateRoute", "rateLimiter.futureAcquire() is false");
            return null;
        }

        QueryPredictRouteResponseType queryPredictRouteResponseType = dcsMapDomainServiceProxy.queryEstimateRoute(getQueryPredictRouteRequestType(cityId, userOrderId, position));
        if (Objects.isNull(queryPredictRouteResponseType)) {
            logger.warn("GeoGatewayImpl.queryEstimateRoute", "queryPredictRouteResponseType is null");
            return null;
        }
        if (queryPredictRouteResponseType.getResponseResult() == null) {
            logger.warn("GeoGatewayImpl.queryEstimateRoute", "queryPredictRouteResponseType.getResponseResult() is null");
            return null;
        }
        if (!ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(queryPredictRouteResponseType.getResponseResult().getReturnCode())) {
            logger.warn("GeoGatewayImpl.queryEstimateRoute", "queryPredictRouteResponseType.getResponseResult().getReturnCode() is not success");
            return null;
        }

        String fromHash = GeoHashUtil.buildGeoHash(position.getFromLongitude(), position.getFromLatitude());
        String toHash = GeoHashUtil.buildGeoHash(position.getToLongitude(), position.getToLatitude());
        Integer duration = queryPredictRouteResponseType.getDuration();
        Integer distance = queryPredictRouteResponseType.getDistance();
        if (Objects.isNull(duration) || Objects.isNull(distance)) {
            logger.warn("GeoGatewayImpl.queryEstimateRoute", "duration or distance is null");
            return null;
        }

        String hash = Position.hash(fromHash, toHash);
        String timeHash = Position.timeHash(fromHash, toHash, position.getDepartureTime());

        return new Route(hash, timeHash, distance.doubleValue() / 1000, duration.doubleValue() / 60);
    }

    private void metricLog(Integer cityId, Position position) {
        try {

            // 判断埋点城市是否在配置中
            if (!delayDspCommonQConfig.isMonitorCity(cityId)) {
                return;
            }

            // QPS统计 - 总体调用量
            MetricsUtil.recordValue("query.estimate.route.qps", 1);

            // QPS统计 - 按城市维度
            MetricsUtil.recordValueWithTag("query.estimate.route.city.qps", 1, "cityId", cityId.toString());

            // QPS统计 - 按时段维度（如果有出发时间）
            if (Objects.nonNull(position.getDepartureTime())) {
                String hourTag = DateUtil.formatDate(position.getDepartureTime(), "HH");
                String dayHourTag = DateUtil.formatDate(position.getDepartureTime(), DateUtil.HOUR_FMT);

                // 按小时统计（0-23）
                MetricsUtil.recordValueWithTag("query.estimate.route.hour.qps", 1, "hour", hourTag);

                // 按完整时间段统计（yyyy-MM-dd-HH）
                MetricsUtil.recordValueWithTag("query.estimate.route.time.period.qps", 1, "timePeriod", dayHourTag);

                // 按城市+小时组合维度统计
                String cityHourTag = cityId + "-" + hourTag;
                MetricsUtil.recordValueWithTag("query.estimate.route.city.hour.qps", 1, "cityHour", cityHourTag);

                // 按城市+完整时间段组合维度统计
                String cityTimePeriodTag = cityId + "-" + dayHourTag;
                MetricsUtil.recordValueWithTag("query.estimate.route.city.time.period.qps", 1, "cityTimePeriod", cityTimePeriodTag);
            }
        } catch (Exception e) {
            logger.error("GeoGatewayImpl.queryEstimateRoute", e);
        }
    }


    private static QueryPredictRouteRequestType getQueryPredictRouteRequestType(Integer cityId, String userOrderId, Position position) {
        QueryPredictRouteRequestType routeRequestType = new QueryPredictRouteRequestType();
        BaseGpsDTO from = new BaseGpsDTO();
        from.setLatitude(BigDecimal.valueOf(position.getFromLatitude()));
        from.setLongitude(BigDecimal.valueOf(position.getFromLongitude()));
        from.setCoordType(position.getFromCoordsys());
        from.setCityId(cityId.longValue());
        routeRequestType.setOrigin(from);

        BaseGpsDTO to = new BaseGpsDTO();
        to.setLatitude(BigDecimal.valueOf(position.getToLatitude()));
        to.setLongitude(BigDecimal.valueOf(position.getToLongitude()));
        to.setCoordType(position.getToCoordsys());
        to.setCityId(cityId.longValue());

        routeRequestType.setDestination(to);
        if (Objects.nonNull(position.getDepartureTime())) {
            routeRequestType.setDepartureTime(position.getDepartureTime().getTime());
            ExtendInfoDTO extendInfoDTO = new ExtendInfoDTO();
            extendInfoDTO.setForceGaodeFuture(true);
            routeRequestType.setExtendInfoDTO(extendInfoDTO);
        }
        routeRequestType.setOrderId(userOrderId);
        return routeRequestType;
    }

    @Override
    public Route queryRoute(Long taskId, Position position) {
        try {
            if (Objects.equals(position.getFromHash(), position.getToHash())) {
                return new Route(position.hash(), 0, 0);
            }
            String key = Route.toKey(taskId, position.hash());
            String v = caffeineCache.get(key, k -> (trocksProviderProxy.get(k)));
            if (StringUtils.isBlank(v)) {
                MetricsUtil.recordValue("query.route.cache.null", 1);
                return new Route(position.hash(), Integer.MAX_VALUE, Integer.MAX_VALUE);
            }
            List<String> list = Splitter.on(CommonConstant.PLACEHOLDER).splitToList(v);
            return new Route(position.hash(), Double.valueOf(list.get(0)), Double.valueOf(list.get(1)));
        } catch (Exception e) {
            logger.error("GeoGatewayImpl.queryRoute", e);
        }
        MetricsUtil.recordValue("query.route.cache.null", 1);
        return new Route(position.hash(), Integer.MAX_VALUE, Integer.MAX_VALUE);
    }



    private QueryDistanceBatchRequestType buildQueryDistanceBatchRequestType(Integer cityId, List<Position> list) {
        List<BaseLatLngPairDTO> dtos = Lists.newArrayList();
        for (Position position : list) {
            BaseLatLngPairDTO dto = new BaseLatLngPairDTO();
            dto.setCid(cityId.longValue());
            dto.setCoordType(position.getFromCoordsys());
            dto.setOriginLongitude(BigDecimal.valueOf(position.getFromLongitude()));
            dto.setOriginLatitude(BigDecimal.valueOf(position.getFromLatitude()));
            dto.setDestinationLongitude(BigDecimal.valueOf(position.getToLongitude()));
            dto.setDestinationLatitude(BigDecimal.valueOf(position.getToLatitude()));
            dtos.add(dto);
        }
        QueryDistanceBatchRequestType request = new QueryDistanceBatchRequestType();
        request.setGpsPair(dtos);
        return request;
    }

    private List<Route> buildRoute(QueryDistanceBatchResponseType response) {
        List<Route> list = Lists.newArrayList();
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getResults())) {
            return list;
        }
        for (GaodeDistanceInfoDTO dto : response.getResults()) {
            String fromHash = GeoHashUtil.buildGeoHash(dto.getOriginLongitude().doubleValue(), dto.getOriginLatitude().doubleValue());
            String toHash = GeoHashUtil.buildGeoHash(dto.getDestinationLongitude().doubleValue(), dto.getDestinationLatitude().doubleValue());
            Route route = new Route(Position.hash(fromHash, toHash), (double) dto.getDistance() / 1000, (double) dto.getDuration() / 60);
            list.add(route);
        }
        return list;
    }

    /**
     * 发送延迟消息，在真实出发时间点获取实时预估路径
     */
    private void sendDelayedMonitorMessage(GaoDeFutureDTO gaoDeFutureMonitorDTO) {
        try {
            // 计算延迟时间：出发时间与现在时间的时间差
            long delayTime = gaoDeFutureMonitorDTO.getDepartureTime().getTime() - System.currentTimeMillis();
            // 只有当出发时间在未来时才发送延迟消息
            if (delayTime > 0) {
                // 准备消息体数据
                Map<String, Object> messageData = new HashMap<>();
                messageData.put("data", JsonUtil.toJson(gaoDeFutureMonitorDTO));
                // 发送延迟消息
                messageProducer.sendDelayMessage(CommonConstant.GAODE_FUTURE_MONITOR_DELAY_SUBJECT, messageData, delayTime);
                logger.info("GeoGatewayImpl.sendDelayedMonitorMessage", "send sucess, orderId:{}, cityId {}, delayTime:{}", gaoDeFutureMonitorDTO.getUserOrderId(), gaoDeFutureMonitorDTO.getCityId(), delayTime);
            }
        } catch (Exception e) {
            logger.error("GeoGatewayImpl.sendDelayedMonitorMessage", "send failed", e);
        }
    }
}
